#!/usr/bin/env python3

import os
import traceback

# Set cache directory
os.environ['HF_HOME'] = 'E:\\hf_cache'

print("Testing model loading...")
print(f"HF_HOME set to: {os.environ.get('HF_HOME')}")

try:
    print("1. Testing text encoder loading...")
    from transformers import LlamaModel, CLIPTextModel, LlamaTokenizerFast, CLIPTokenizer
    
    # This should use the cached models we downloaded
    print("   Loading text encoder...")
    text_encoder = CLIPTextModel.from_pretrained(
        "hunyuanvideo-community/HunyuanVideo", 
        subfolder="text_encoder",
        torch_dtype="auto"
    )
    print("✅ Text encoder loaded successfully!")
    
except Exception as e:
    print("❌ Text encoder loading ERROR:")
    print(traceback.format_exc())

try:
    print("2. Testing transformer model loading...")
    from diffusers_helper.models.hunyuan_video_packed import HunyuanVideoTransformer3DModelPacked
    
    print("   Loading transformer...")
    transformer = HunyuanVideoTransformer3DModelPacked.from_pretrained(
        "hunyuanvideo-community/HunyuanVideo", 
        subfolder="transformer",
        torch_dtype="auto"
    )
    print("✅ Transformer loaded successfully!")
    
except Exception as e:
    print("❌ Transformer loading ERROR:")
    print(traceback.format_exc())

try:
    print("3. Testing VAE loading...")
    from diffusers import AutoencoderKLHunyuanVideo
    
    print("   Loading VAE...")
    vae = AutoencoderKLHunyuanVideo.from_pretrained(
        "hunyuanvideo-community/HunyuanVideo", 
        subfolder="vae",
        torch_dtype="auto"
    )
    print("✅ VAE loaded successfully!")
    
except Exception as e:
    print("❌ VAE loading ERROR:")
    print(traceback.format_exc())

print("Model loading test complete!")
