#!/usr/bin/env python3

print("Testing imports...")

# Test basic imports
try:
    import torch
    print("✅ PyTorch OK")
except Exception as e:
    print("❌ PyTorch ERROR:", e)

try:
    import gradio as gr
    print("✅ Gradio OK")
except Exception as e:
    print("❌ Gradio ERROR:", e)

try:
    from diffusers import AutoencoderKLHunyuanVideo
    print("✅ Diffusers AutoencoderKLHunyuanVideo OK")
except Exception as e:
    print("❌ Diffusers AutoencoderKLHunyuanVideo ERROR:", e)

# Test custom imports
try:
    from diffusers_helper.hf_login import login
    print("✅ diffusers_helper.hf_login OK")
except Exception as e:
    print("❌ diffusers_helper.hf_login ERROR:", e)

try:
    from diffusers_helper.hunyuan import encode_prompt_conds
    print("✅ diffusers_helper.hunyuan OK")
except Exception as e:
    print("❌ diffusers_helper.hunyuan ERROR:", e)

try:
    from diffusers_helper.models.hunyuan_video_packed import HunyuanVideoTransformer3DModelPacked
    print("✅ diffusers_helper.models.hunyuan_video_packed OK")
except Exception as e:
    print("❌ diffusers_helper.models.hunyuan_video_packed ERROR:", e)

print("Import testing complete!")
