import os
import torch
import gc

# Set cache to E drive
os.environ['HF_HOME'] = 'E:\\hf_cache'
os.environ['HUGGINGFACE_HUB_CACHE'] = 'E:\\hf_cache'
os.environ['TRANSFORMERS_CACHE'] = 'E:\\hf_cache'

print("Testing minimal model loading...")
print(f"Available memory: {torch.cuda.get_device_properties(0).total_memory if torch.cuda.is_available() else 'CPU mode'}")

try:
    print("Loading tokenizer...")
    from transformers import LlamaTokenizerFast
    tokenizer = LlamaTokenizerFast.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='tokenizer')
    print("✅ Tokenizer loaded successfully")
    
    print("Loading text encoder (this may take a while)...")
    from transformers import LlamaModel
    text_encoder = LlamaModel.from_pretrained(
        "hunyuanvideo-community/HunyuanVideo", 
        subfolder='text_encoder', 
        torch_dtype=torch.float16,
        low_cpu_mem_usage=True,
        device_map="auto"
    )
    print("✅ Text encoder loaded successfully")
    
    print("🎉 Basic model loading successful!")
    print("You can now try running the full application.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    print("You need to increase virtual memory or use a system with more RAM.")
